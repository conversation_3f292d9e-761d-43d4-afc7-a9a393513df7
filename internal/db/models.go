package db

import (
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// don't use soft delete
type BaseModel struct {
	ID        int64     `gorm:"type:bigint;primaryKey;autoIncrement" json:"id"`
	CreatedAt time.Time `gorm:"type:timestamp with time zone;not null;default:CURRENT_TIMESTAMP" json:"-"`
	UpdatedAt time.Time `gorm:"type:timestamp with time zone;not null;default:CURRENT_TIMESTAMP" json:"-"`
}

// JSONB is a type for handling PostgreSQL JSONB data
type JSONB map[string]interface{}

// Value implements the driver.Valuer interface for JSONB
func (j JSONB) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// <PERSON>an implements the sql.Scanner interface for JSONB
func (j *JSONB) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, j)
}

// TwitterUser represents a Twitter user
type TwitterUser struct {
	BaseModel
	UserID          string    `gorm:"column:user_id;uniqueIndex;type:varchar(255)"`
	ScreenName      string    `gorm:"column:screen_name;uniqueIndex;type:varchar(100);not null"`
	Name            string    `gorm:"column:name;type:varchar(255);not null"`
	FollowersCount  int       `gorm:"column:followers_count;type:integer"`
	IsVerified      bool      `gorm:"column:is_verified;type:boolean;default:false"`
	ProfileImageURL string    `gorm:"column:profile_image_url;type:text"`
	FetchedAt       time.Time `gorm:"column:fetched_at;type:timestamp with time zone;default:current_timestamp"`

	// Relationships
	Tweets []Tweet `gorm:"foreignKey:UserIDFK;references:UserID"`
}

// TableName specifies the table name for TwitterUser
func (TwitterUser) TableName() string {
	return "twitter_users"
}

// Tag represents a tag used for classification
type Tag struct {
	BaseModel
	Name        string    `gorm:"column:name;uniqueIndex;type:citext"`
	Description string    `gorm:"column:description;type:text"`
	CreatedAt   time.Time `gorm:"column:created_at;type:timestamp with time zone;not null;default:CURRENT_TIMESTAMP"`
}

// TableName specifies the table name for Tag
func (Tag) TableName() string {
	return "tags"
}

// TweetTag represents the many-to-many relationship between tweets and tags
type TweetTag struct {
	TweetID int64 `gorm:"type:bigint;primaryKey;column:tweet_id"`
	TagID   int64 `gorm:"type:bigint;primaryKey;column:tag_id"`
}

// TableName specifies the table name for TweetTag
func (TweetTag) TableName() string {
	return "tweet_tags"
}

// RecognizedCATag represents the many-to-many relationship between recognized CAs and tags
type RecognizedCATag struct {
	RecognizedCAID int64 `gorm:"type:bigint;primaryKey;column:recognized_ca_id"`
	TagID          int64 `gorm:"type:bigint;primaryKey;column:tag_id"`
}

// TableName specifies the table name for RecognizedCATag
func (RecognizedCATag) TableName() string {
	return "recognized_ca_tags"
}

// CollectionTag represents the relationship between collection tags and Twitter usernames
type CollectionTag struct {
	BaseModel
	TagName         string `gorm:"column:tag_name;type:varchar(255);not null;uniqueIndex:idx_collection_tags_username_tag,priority:2"`
	TwitterUsername string `gorm:"column:twitter_username;type:varchar(255);not null;uniqueIndex:idx_collection_tags_username_tag,priority:1"`
}

// TableName specifies the table name for CollectionTag
func (CollectionTag) TableName() string {
	return "collection_tags"
}

// Tweet represents a tweet
type Tweet struct {
	BaseModel
	TweetID                string     `gorm:"column:tweet_id;uniqueIndex:idx_tweet_published,priority:1;type:varchar(255)"`
	UserIDFK               string     `gorm:"column:user_id_fk;type:varchar(255);not null;index"`
	TextContent            string     `gorm:"column:text_content;type:text;not null"`
	FullTweetJSON          JSONB      `gorm:"column:full_tweet_json;type:jsonb;index:idx_tweets_full_tweet_json,type:gin"`
	PublishedAt            time.Time  `gorm:"column:published_at;uniqueIndex:idx_tweet_published,priority:2;type:timestamp with time zone;not null"`
	ViewsCount             int        `gorm:"column:views_count;type:bigint"`
	ReplyCount             int        `gorm:"column:reply_count;type:integer;default:0"`
	RetweetCount           int        `gorm:"column:retweet_count;type:integer;default:0"`
	FavoriteCount          int        `gorm:"column:favorite_count;type:integer;default:0"`
	BookmarkCount          int        `gorm:"column:bookmark_count;type:integer;default:0"`
	ContainsTargetKeyword  bool       `gorm:"column:contains_target_keyword;type:boolean;default:false;index"`
	IngestedAt             time.Time  `gorm:"column:ingested_at;type:timestamp with time zone;default:current_timestamp"`
	TweetType              string     `gorm:"column:tweet_type;type:varchar(50);default:'tweet'"`         // tweet or article
	ContentType            string     `gorm:"column:content_type;type:varchar(50);default:'tweet'"`       // tweet or reply or quote
	SourceListType         string     `gorm:"column:source_list_type;type:varchar(50);default:'unknown'"` // KOLs or Projects
	AIJudgment             string     `gorm:"column:ai_judgment;type:varchar(10);default:'NO'"`           // YES or NO
	ArticleTitle           string     `gorm:"column:article_title;type:text"`                             // Article title for article type tweets
	ArticlePreviewText     string     `gorm:"column:article_preview_text;type:text"`                      // Article preview text for article type tweets
	ArticleCoverURL        string     `gorm:"column:article_cover_url;type:text"`                         // Article cover URL for article type tweets
	FullArticleJSON        JSONB      `gorm:"column:full_article_json;type:jsonb;index:idx_tweets_full_article_json,type:gin"`
	BulletPoints           JSONB      `gorm:"column:bullet_points;type:jsonb"`                                    // Bullet points for article type tweets
	PeriodicallyUpdatedAt  *time.Time `gorm:"column:periodically_updated_at;type:timestamp with time zone;index"` // Tracks when the tweet was last updated by the periodic updater
	IsProductUpdate        bool       `gorm:"column:is_product_update;type:boolean;default:false"`
	IsBusinessData         bool       `gorm:"column:is_business_data;type:boolean;default:false"`
	IsEcosystemPartnership bool       `gorm:"column:is_ecosystem_partnership;type:boolean;default:false"`
	IsProfitOpportunity    bool       `gorm:"column:is_profit_opportunity;type:boolean;default:false"`
	IsIndustryEvent        bool       `gorm:"column:is_industry_event;type:boolean;default:false"`
	IsOthers               bool       `gorm:"column:is_others;type:boolean;default:false"`

	// Telegram notification tracking
	TelegramNotificationSent   bool       `gorm:"column:telegram_notification_sent;type:boolean;default:false;index"`
	TelegramNotificationSentAt *time.Time `gorm:"column:telegram_notification_sent_at;type:timestamp with time zone"`

	// Duplicate content detection
	IsOutdated        bool       `gorm:"column:is_outdated;type:boolean;default:false;index"`
	OutdatedAt        *time.Time `gorm:"column:outdated_at;type:timestamp with time zone"`
	OutdatedByTweetID string     `gorm:"column:outdated_by_tweet_id;type:varchar(255)"`

	// Relationships
	User         *TwitterUser   `gorm:"foreignKey:UserIDFK;references:UserID"`
	ExtractedCAs []RecognizedCA `gorm:"many2many:tweet_contract_addresses;"`
	Tags         []Tag          `gorm:"many2many:tweet_tags;"`

	// Non-GORM field for collection tags (populated manually)
	CollectionTags []string `gorm:"-"`
}

// TableName specifies the table name for Tweet
func (Tweet) TableName() string {
	return "tweets"
}

type RecognizedCA struct {
	BaseModel
	CAAddress            string     `gorm:"column:ca_address;uniqueIndex;type:varchar(255)"`
	ChainType            string     `gorm:"column:chain_type;type:varchar(100);not null;index"`
	TokenNameHint        string     `gorm:"column:token_name_hint;type:varchar(255)"`
	AddedAt              time.Time  `gorm:"column:added_at;type:timestamp with time zone;default:current_timestamp"`
	LastTweetAt          time.Time  `gorm:"column:last_tweet_at;type:timestamp with time zone;default:current_timestamp"`
	LastCheckedForDataAt *time.Time `gorm:"column:last_checked_for_data_at;type:timestamp with time zone;index"`
	ReferenceCount       int        `gorm:"column:reference_count;type:int;default:0"`

	// Relationships
	Tweets       []Tweet        `gorm:"many2many:tweet_contract_addresses;"`
	TokenDetails []TokenDetails `gorm:"foreignKey:CAAddressFK;references:CAAddress"` // Changed to one-to-many
	Tags         []Tag          `gorm:"many2many:recognized_ca_tags;"`
}

// TableName specifies the table name for RecognizedCA
func (RecognizedCA) TableName() string {
	return "recognized_cas"
}

// TokenDetails represents token details fetched from DexScreener or other sources
type TokenDetails struct {
	BaseModel
	CAAddressFK         string     `gorm:"column:ca_address_fk;type:varchar(255);index;uniqueIndex:idx_token_details_ca_chain,priority:1"` // Added composite uniqueIndex
	ChainID             string     `gorm:"column:chain_id;type:varchar(100);not null;index;uniqueIndex:idx_token_details_ca_chain,priority:2"`
	Source              string     `gorm:"column:source;type:varchar(50);not null;default:'dexscreener'"`
	TokenName           string     `gorm:"column:token_name;type:varchar(255)"`
	Symbol              string     `gorm:"column:symbol;type:varchar(50)"`
	TokenLogoURL        string     `gorm:"column:token_logo_url;type:text"`
	TokenTwitterURL     string     `gorm:"column:token_twitter_url;type:text"`
	PairCreatedAt       *time.Time `gorm:"column:pair_created_at;type:timestamp with time zone"`
	TokenCreatedAt      *time.Time `gorm:"column:token_created_at;type:timestamp with time zone"`
	HolderCount         *int64     `gorm:"column:holder_count;type:bigint"`
	MarketCapUSD        *float64   `gorm:"column:market_cap_usd;type:numeric(30,8)"`
	PriceUSD            *float64   `gorm:"column:price_usd;type:numeric(30,18)"`
	FullDexScreenerJSON string     `gorm:"column:full_dexscreener_json;type:jsonb;default:'{}';index:idx_token_details_full_dexscreener_json,type:gin"`
	LastUpdatedAt       time.Time  `gorm:"column:last_updated_at;type:timestamp with time zone;default:current_timestamp"`

	// Relationships
	RecognizedCA *RecognizedCA `gorm:"foreignKey:CAAddressFK;references:CAAddress"`
}

// TableName specifies the table name for TokenDetails
func (TokenDetails) TableName() string {
	return "token_details"
}

// RedisService interface defines the Redis operations needed by the database layer
type RedisService interface {
	GetClient() interface{} // We'll use type assertion to get *redis.Client
}

// Database represents a database connection
type Database struct {
	DB           *gorm.DB
	redisService RedisService // Interface for Redis operations
}

// Helper methods to make Database struct methods work with the embedded DB

func (d *Database) Clauses(conds ...clause.Expression) *gorm.DB {
	return d.DB.Clauses(conds...)
}

func (d *Database) Where(query interface{}, args ...interface{}) *gorm.DB {
	return d.DB.Where(query, args...)
}

func (d *Database) Preload(query string, args ...interface{}) *gorm.DB {
	return d.DB.Preload(query, args...)
}

func (d *Database) Raw(sql string, values ...interface{}) *gorm.DB {
	return d.DB.Raw(sql, values...)
}

func (d *Database) Order(value interface{}) *gorm.DB {
	return d.DB.Order(value)
}

func (d *Database) Limit(limit int) *gorm.DB {
	return d.DB.Limit(limit)
}

func (d *Database) Offset(offset int) *gorm.DB {
	return d.DB.Offset(offset)
}

func (d *Database) Find(dest interface{}, conds ...interface{}) *gorm.DB {
	return d.DB.Find(dest, conds...)
}

func (d *Database) First(dest interface{}, conds ...interface{}) *gorm.DB {
	return d.DB.First(dest, conds...)
}

func (d *Database) Create(value interface{}) *gorm.DB {
	return d.DB.Create(value)
}

func (d *Database) Delete(value interface{}, conds ...interface{}) *gorm.DB {
	return d.DB.Delete(value, conds...)
}

func (d *Database) Model(value interface{}) *gorm.DB {
	return d.DB.Model(value)
}

func (d *Database) Transaction(fc func(tx *gorm.DB) error, opts ...*sql.TxOptions) error {
	return d.DB.Transaction(fc, opts...)
}

func (d *Database) Exec(sql string, values ...interface{}) *gorm.DB {
	return d.DB.Exec(sql, values...)
}

// DuneTwitterBinding represents the binding between Twitter users and Dune queries
type DuneTwitterBinding struct {
	BaseModel
	DuneQueryID       string      `gorm:"column:dune_query_id;type:varchar(255);not null;uniqueIndex:idx_dune_twitter_bindings_query_id_unique" json:"dune_query_id"`
	TwitterUserName   string      `gorm:"column:twitter_user_name;type:varchar(255);not null" json:"twitter_user_name"`
	ChainIDs          StringArray `gorm:"column:chain_ids;type:jsonb;default:'[]'" json:"chain_ids"`
	ProjectName       string      `gorm:"column:project_name;type:varchar(500)" json:"project_name"`
	ProjectLogo       string      `gorm:"column:project_logo;type:text" json:"project_logo"`
	ProjectWebsite    string      `gorm:"column:project_website;type:text" json:"project_website"`
	ContractAddresses StringArray `gorm:"column:contract_addresses;type:jsonb;default:'[]'" json:"contract_addresses"`
	Tags              StringArray `gorm:"column:tags;type:jsonb;default:'[]'" json:"tags"`

	// Relationships with categories
	BindingCategories []DuneBindingCategory `gorm:"foreignKey:BindingID;references:ID;constraint:OnDelete:CASCADE" json:"-"`
	Categories        []DuneCategory        `gorm:"many2many:dune_binding_categories;foreignKey:ID;joinForeignKey:binding_id;References:ID;joinReferences:category_id;" json:"categories,omitempty"`
}

// TableName specifies the table name for DuneTwitterBinding
func (DuneTwitterBinding) TableName() string {
	return "dune_twitter_bindings"
}

// CategoryQueryMapping represents a mapping between category and query
type CategoryQueryMapping struct {
	CategoryID  int64  `json:"category_id" binding:"required" example:"1" description:"Category ID"`
	DuneQueryID string `json:"dune_query_id" binding:"required" example:"3234567" description:"Dune query ID"`
}

// DuneBindingResponse represents the response structure for Dune binding queries
type DuneBindingResponse struct {
	ID                int64                  `json:"id"`
	CategoryQueries   []CategoryQueryMapping `json:"category_queries" description:"Array of category-query mappings"`
	TwitterUserName   string                 `json:"twitter_user_name"`
	ChainIDs          StringArray            `json:"chain_ids"`
	ProjectName       string                 `json:"project_name"`
	ProjectLogo       string                 `json:"project_logo"`
	ProjectWebsite    string                 `json:"project_website"`
	ContractAddresses StringArray            `json:"contract_addresses"`
	Tags              StringArray            `json:"tags"`
}

// DuneCategory represents a custom category for dune bindings
type DuneCategory struct {
	BaseModel
	Name        string `gorm:"column:name;type:varchar(255);not null;uniqueIndex" json:"name"`
	Description string `gorm:"column:description;type:text" json:"description"`

	// Relationships
	Fields   []DuneCategoryField   `gorm:"foreignKey:CategoryID;references:ID;constraint:OnDelete:CASCADE" json:"fields"`
	Bindings []DuneBindingCategory `gorm:"foreignKey:CategoryID;references:ID;constraint:OnDelete:CASCADE" json:"-"`
}

// TableName specifies the table name for DuneCategory
func (DuneCategory) TableName() string {
	return "dune_categories"
}

// DuneCategoryField represents a field definition in a category
type DuneCategoryField struct {
	BaseModel
	CategoryID   int64  `gorm:"column:category_id;type:bigint;not null;index" json:"category_id"`
	Key          string `gorm:"column:key;type:varchar(255);not null" json:"key"`
	Type         string `gorm:"column:type;type:varchar(100);not null" json:"type"` // int, string, float, etc.
	Note         string `gorm:"column:note;type:text" json:"note"`
	Required     bool   `gorm:"column:required;type:boolean;default:false" json:"required"`
	AggregateSum *bool  `gorm:"column:aggregate_sum;type:boolean;default:true;not null" json:"aggregate_sum"` // Controls whether to use sum aggregation for comparison

	// Add unique constraint for category_id + key combination
	Category *DuneCategory `gorm:"foreignKey:CategoryID;references:ID;constraint:OnDelete:CASCADE" json:"-"`
}

// TableName specifies the table name for DuneCategoryField
func (DuneCategoryField) TableName() string {
	return "dune_category_fields"
}

// DuneBindingCategory represents the many-to-many relationship between bindings and categories
type DuneBindingCategory struct {
	BindingID  int64 `gorm:"type:bigint;primaryKey;column:binding_id"`
	CategoryID int64 `gorm:"type:bigint;primaryKey;column:category_id"`

	// Foreign key references
	Binding  *DuneTwitterBinding `gorm:"foreignKey:BindingID;references:ID;constraint:OnDelete:CASCADE" json:"-"`
	Category *DuneCategory       `gorm:"foreignKey:CategoryID;references:ID;constraint:OnDelete:CASCADE" json:"-"`
}

// TableName specifies the table name for DuneBindingCategory
func (DuneBindingCategory) TableName() string {
	return "dune_binding_categories"
}

// DuneQueryResult represents the results from Dune queries with dynamic fields
type DuneQueryResult struct {
	BaseModel
	DuneQueryID string    `gorm:"column:dune_query_id;type:varchar(255);not null;index:idx_dune_query_results_query_id" json:"dune_query_id"`
	QueryDate   time.Time `gorm:"column:query_date;type:date;not null;index:idx_dune_query_results_date" json:"query_date"`
	// Legacy fields for backward compatibility
	ContractInteraction int `gorm:"column:contract_interaction;type:int;default:0" json:"contract_interaction"`
	Users               int `gorm:"column:users;type:int;default:0" json:"users"`
	// New dynamic data field to store all category field values
	Data JSONB `gorm:"column:data;type:jsonb;default:'{}';index:idx_dune_query_results_data,type:gin" json:"data"`
}

// TableName specifies the table name for DuneQueryResult
func (DuneQueryResult) TableName() string {
	return "dune_query_results"
}

// StringArray is a custom type for handling JSON string arrays
type StringArray []string

// Value implements the driver.Valuer interface for StringArray
func (s StringArray) Value() (driver.Value, error) {
	if s == nil {
		return nil, nil
	}
	return json.Marshal(s)
}

// Scan implements the sql.Scanner interface for StringArray
func (s *StringArray) Scan(value interface{}) error {
	if value == nil {
		*s = nil
		return nil
	}

	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, s)
	case string:
		return json.Unmarshal([]byte(v), s)
	default:
		return errors.New("cannot scan non-string value into StringArray")
	}
}

func (d *Database) AutoMigrate(dst ...interface{}) error {
	return d.DB.AutoMigrate(dst...)
}

// ExcludedTwitterUser represents Twitter users that should be excluded from statistics and API returns
type ExcludedTwitterUser struct {
	BaseModel
	TwitterUserName string    `gorm:"column:twitter_user_name;type:varchar(255);not null;uniqueIndex:idx_excluded_twitter_users_username" json:"twitter_user_name"`
	Reason          string    `gorm:"column:reason;type:text" json:"reason"`
	ExcludedBy      string    `gorm:"column:excluded_by;type:varchar(255)" json:"excluded_by"`
	ExcludedAt      time.Time `gorm:"column:excluded_at;type:timestamp with time zone;default:current_timestamp" json:"excluded_at"`
}

// TableName specifies the table name for ExcludedTwitterUser
func (ExcludedTwitterUser) TableName() string {
	return "excluded_twitter_users"
}

// Close closes the database connection
func (d *Database) Close() error {
	sqlDB, err := d.DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}
