basePath: /api
definitions:
  api.AnnouncementStatisticsDailyResponse:
    properties:
      count:
        type: integer
      date:
        type: string
    type: object
  api.AnnouncementStatisticsTotalResponse:
    properties:
      change_percentage_24h:
        type: number
      previous_day_total:
        type: integer
      total_announcements:
        type: integer
    type: object
  api.CAResponse:
    properties:
      address:
        type: string
      chain_type:
        type: string
      is_recognized:
        type: boolean
      tags:
        items:
          type: string
        type: array
      token_details:
        description: Changed to array
        items:
          $ref: '#/definitions/api.TokenDetailsResponse'
        type: array
      trade_token_details:
        items:
          $ref: '#/definitions/api.TradeTokenInfo'
        type: array
      tweets:
        items:
          $ref: '#/definitions/api.TweetInfo'
        type: array
    type: object
  api.DuneBindingCategoryRequest:
    properties:
      category_ids:
        items:
          type: integer
        type: array
    required:
    - category_ids
    type: object
  api.DuneBindingRequest:
    description: Request body for creating or updating a Dune Twitter binding with
      category-query mappings
    properties:
      category_queries:
        items:
          $ref: '#/definitions/db.CategoryQueryMapping'
        type: array
      chain_names:
        example:
        - base
        - bsc
        items:
          type: string
        type: array
      contract_addresses:
        example:
        - 0x1234567890123456789012345678901234567890
        items:
          type: string
        type: array
      project_logo:
        example: https://pbs.twimg.com/profile_images/1637832152000000000/1637832152000000000.jpg
        type: string
      project_name:
        example: Elon Musk
        type: string
      project_website:
        example: https://example.com
        type: string
      tags:
        example:
        - nft
        - twitter
        items:
          type: string
        type: array
      twitter_user_name:
        example: elonmusk
        type: string
    required:
    - category_queries
    - twitter_user_name
    type: object
  api.DuneCategoryFieldRequest:
    properties:
      aggregate_sum:
        example: true
        type: boolean
      key:
        example: total_value_locked
        type: string
      note:
        example: Total Value Locked in USD
        type: string
      required:
        example: true
        type: boolean
      type:
        example: float
        type: string
    required:
    - key
    - type
    type: object
  api.DuneCategoryRequest:
    properties:
      description:
        example: DeFi protocol metrics and data
        type: string
      fields:
        items:
          $ref: '#/definitions/api.DuneCategoryFieldRequest'
        type: array
      name:
        example: defi-metrics
        type: string
    required:
    - fields
    - name
    type: object
  api.DuneFetchAllResponse:
    properties:
      duration:
        example: 2.5s
        type: string
      message:
        example: Dune data fetch triggered successfully
        type: string
      query_count:
        example: 5
        type: integer
      status:
        example: success
        type: string
      triggered_at:
        example: 1672531200
        type: integer
      triggered_by:
        example: manual
        type: string
    type: object
  api.ExcludedTwitterUserRequest:
    properties:
      twitter_user_name:
        type: string
    required:
    - twitter_user_name
    type: object
  api.GetTradeTokenInfoResponse:
    properties:
      code:
        type: integer
      data:
        properties:
          list:
            items:
              $ref: '#/definitions/api.TradeTokenInfo'
            type: array
          page:
            type: integer
          page_size:
            type: integer
          total_count:
            type: integer
        type: object
      msg:
        type: string
    type: object
  api.HideListRequest:
    properties:
      usernames:
        items:
          type: string
        type: array
    type: object
  api.NoticeResponse:
    properties:
      is_business_data:
        type: boolean
      is_ecosystem_partnership:
        type: boolean
      is_industry_event:
        type: boolean
      is_others:
        type: boolean
      is_product_update:
        type: boolean
      is_profit_opportunity:
        type: boolean
    type: object
  api.PaginatedAnnouncementStatsResponse:
    properties:
      data:
        items:
          $ref: '#/definitions/api.UserAnnouncementStatsResponse'
        type: array
      page:
        type: integer
      page_size:
        type: integer
      total_count:
        type: integer
      total_pages:
        type: integer
    type: object
  api.TokenDetailsResponse:
    properties:
      chain_id:
        type: string
      holder_count:
        type: integer
      logo_url:
        type: string
      market_cap_usd:
        type: number
      name:
        type: string
      pair_created_at:
        type: integer
      price_usd:
        type: number
      source:
        description: Added source field
        type: string
      symbol:
        type: string
      twitter_url:
        type: string
    type: object
  api.TradeTokenInfo:
    properties:
      address:
        type: string
      ai_report:
        type: string
      banner_url:
        type: string
      chain_id:
        type: integer
      coingecko_url:
        type: string
      coinmarketcap_url:
        type: string
      creation_date:
        type: string
      creator_x_username:
        type: string
      decimals:
        type: integer
      description:
        type: string
      discord_url:
        type: string
      followers_count:
        type: integer
      github_url:
        type: string
      influencers_count:
        type: integer
      instagram_username:
        type: string
      is_verified:
        type: boolean
      is_watched:
        type: boolean
      logo_url:
        type: string
      market_cap:
        type: string
      medium_url:
        type: string
      mobile_banner_url:
        type: string
      name:
        type: string
      price_change_in_1hours:
        type: string
      price_change_in_6hours:
        type: string
      price_change_in_24hours:
        type: string
      price_in_usd:
        type: string
      profile:
        type: string
      project_url:
        type: string
      projects_count:
        type: integer
      rank:
        type: integer
      reddit_url:
        type: string
      research_report:
        type: string
      slug:
        type: string
      status:
        type: integer
      symbol:
        type: string
      tags:
        items:
          properties:
            color:
              type: string
            name:
              type: string
            rank:
              type: integer
            type:
              type: integer
          type: object
        type: array
      telegram_url:
        type: string
      tiktok_url:
        type: string
      top_20_followers:
        items:
          properties:
            avatar:
              type: string
            name:
              type: string
            username:
              type: string
          type: object
        type: array
      total_buy_count_24hours:
        type: string
      total_buyer_count_24hours:
        type: string
      total_liquidity:
        type: string
      total_makers_count_24hours:
        type: string
      total_sell_count_24hours:
        type: string
      total_seller_count_24hours:
        type: string
      total_supply:
        type: string
      total_tx_count_24hours:
        type: string
      total_volume_in_1hours:
        type: string
      total_volume_in_6hours:
        type: string
      total_volume_in_24hours:
        type: string
      twitter_score:
        type: string
      twitter_user_id:
        type: string
      twitter_username:
        type: string
      type:
        type: integer
      venture_capitals_count:
        type: integer
      warpcast_url:
        type: string
    type: object
  api.TweetInfo:
    properties:
      article_cover_url:
        type: string
      article_preview_text:
        type: string
      article_title:
        type: string
      bookmark_count:
        type: integer
      bullet_points: {}
      collection_tags:
        items:
          type: string
        type: array
      content_type:
        description: '"tweet" or "article"'
        type: string
      favorite_count:
        type: integer
      id:
        type: string
      images:
        items:
          type: string
        type: array
      notices:
        $ref: '#/definitions/api.NoticeResponse'
      published_at:
        type: integer
      reply_count:
        type: integer
      retweet_count:
        type: integer
      source_list_type:
        description: '"KOLs" or "Projects"'
        type: string
      tags:
        items:
          type: string
        type: array
      text:
        type: string
      user:
        $ref: '#/definitions/api.UserResponse'
      views_count:
        type: integer
    type: object
  api.TweetResponse:
    properties:
      article_cover_url:
        type: string
      article_preview_text:
        type: string
      article_title:
        type: string
      bookmark_count:
        type: integer
      bullet_points: {}
      collection_tags:
        items:
          type: string
        type: array
      content_type:
        description: '"tweet" or "article"'
        type: string
      contract_addresses:
        items:
          $ref: '#/definitions/api.CAResponse'
        type: array
      favorite_count:
        type: integer
      id:
        type: string
      images:
        items:
          type: string
        type: array
      notices:
        $ref: '#/definitions/api.NoticeResponse'
      published_at:
        type: integer
      reply_count:
        type: integer
      retweet_count:
        type: integer
      source_list_type:
        description: '"KOLs" or "Projects"'
        type: string
      tags:
        items:
          type: string
        type: array
      text:
        type: string
      user:
        $ref: '#/definitions/api.UserResponse'
      views_count:
        type: integer
    type: object
  api.TwitterUserInfo:
    properties:
      name:
        type: string
      profile_image_url:
        type: string
      screen_name:
        type: string
      tags:
        items:
          type: string
        type: array
      user_id:
        type: string
    type: object
  api.UserAnnouncementStatsResponse:
    properties:
      business_data_count:
        type: integer
      days_since_last_tweet:
        type: integer
      ecosystem_partnership_count:
        type: integer
      industry_events_count:
        type: integer
      others_count:
        type: integer
      product_updates_count:
        type: integer
      profit_opportunity_count:
        type: integer
      total_announcements_count:
        type: integer
      twitter_user:
        $ref: '#/definitions/api.TwitterUserInfo'
    type: object
  api.UserResponse:
    properties:
      followers_count:
        type: integer
      id:
        type: string
      is_verified:
        type: boolean
      name:
        type: string
      profile_image_url:
        type: string
      screen_name:
        type: string
    type: object
  db.CategoryQueryMapping:
    properties:
      category_id:
        example: 1
        type: integer
      dune_query_id:
        example: "3234567"
        type: string
    required:
    - category_id
    - dune_query_id
    type: object
  db.DuneBindingResponse:
    properties:
      category_queries:
        items:
          $ref: '#/definitions/db.CategoryQueryMapping'
        type: array
      chain_ids:
        items:
          type: string
        type: array
      contract_addresses:
        items:
          type: string
        type: array
      id:
        type: integer
      project_logo:
        type: string
      project_name:
        type: string
      project_website:
        type: string
      tags:
        items:
          type: string
        type: array
      twitter_user_name:
        type: string
    type: object
  db.DuneCategory:
    properties:
      description:
        type: string
      fields:
        description: Relationships
        items:
          $ref: '#/definitions/db.DuneCategoryField'
        type: array
      id:
        type: integer
      name:
        type: string
    type: object
  db.DuneCategoryField:
    properties:
      aggregate_sum:
        description: Controls whether to use sum aggregation for comparison
        type: boolean
      category_id:
        type: integer
      id:
        type: integer
      key:
        type: string
      note:
        type: string
      required:
        type: boolean
      type:
        description: int, string, float, etc.
        type: string
    type: object
  db.DuneTwitterBinding:
    properties:
      categories:
        items:
          $ref: '#/definitions/db.DuneCategory'
        type: array
      chain_ids:
        items:
          type: string
        type: array
      contract_addresses:
        items:
          type: string
        type: array
      dune_query_id:
        type: string
      id:
        type: integer
      project_logo:
        type: string
      project_name:
        type: string
      project_website:
        type: string
      tags:
        items:
          type: string
        type: array
      twitter_user_name:
        type: string
    type: object
  db.ExcludedTwitterUser:
    properties:
      excluded_at:
        type: string
      excluded_by:
        type: string
      id:
        type: integer
      reason:
        type: string
      twitter_user_name:
        type: string
    type: object
  db.JSONB:
    additionalProperties: true
    type: object
  services.AllProjectsDailyFieldSumResponse:
    properties:
      bindings_count:
        description: Number of bindings (total for all projects, category count for
          specific category)
        type: integer
      category_description:
        description: Category description (empty for all projects)
        type: string
      category_id:
        description: Category ID (0 for all projects)
        type: integer
      category_name:
        description: Category name (empty for all projects)
        type: string
      daily_data:
        description: Daily aggregated data (when field is specified)
        items:
          $ref: '#/definitions/services.DailyFieldSumData'
        type: array
      daily_fields_data:
        description: All fields daily data when field is not specified (time in outer
          layer)
        items:
          $ref: '#/definitions/services.DailyFieldsData'
        type: array
      days:
        description: Number of days requested (0 for all)
        type: integer
      field:
        description: Single field name when field is specified
        type: string
      period:
        description: Analysis period
        type: string
    type: object
  services.AllProjectsFieldSumComparisonResponse:
    properties:
      analysis_period:
        type: string
      change_rate:
        description: Single field change rate when field is specified
        type: number
      comparison_period:
        type: string
      current_sum:
        description: Single field sum when field is specified
        type: number
      days:
        type: integer
      field:
        description: Single field name when field is specified
        type: string
      fields_data:
        additionalProperties:
          $ref: '#/definitions/services.FieldSumData'
        description: All fields data when field is not specified
        type: object
      previous_sum:
        description: Single field sum when field is specified
        type: number
      total_bindings_count:
        type: integer
    type: object
  services.CategoryDailyFieldSumResponse:
    properties:
      bindings_count:
        description: Number of bindings (total for all projects, category count for
          specific category)
        type: integer
      category_description:
        description: Category description (empty for all projects)
        type: string
      category_id:
        description: Category ID (0 for all projects)
        type: integer
      category_name:
        description: Category name (empty for all projects)
        type: string
      daily_data:
        description: Daily aggregated data (when field is specified)
        items:
          $ref: '#/definitions/services.DailyFieldSumData'
        type: array
      daily_fields_data:
        description: All fields daily data when field is not specified (time in outer
          layer)
        items:
          $ref: '#/definitions/services.DailyFieldsData'
        type: array
      days:
        description: Number of days requested (0 for all)
        type: integer
      field:
        description: Single field name when field is specified
        type: string
      period:
        description: Analysis period
        type: string
    type: object
  services.CategoryFieldSumComparisonResponse:
    properties:
      analysis_period:
        type: string
      bindings_count:
        type: integer
      category_description:
        type: string
      category_id:
        type: integer
      category_name:
        type: string
      change_rate:
        description: Single field change rate when field is specified
        type: number
      comparison_period:
        type: string
      current_sum:
        description: Single field sum when field is specified
        type: number
      days:
        type: integer
      field:
        description: Single field name when field is specified
        type: string
      fields_data:
        additionalProperties:
          $ref: '#/definitions/services.FieldSumData'
        description: All fields data when field is not specified
        type: object
      previous_sum:
        description: Single field sum when field is specified
        type: number
    type: object
  services.DailyFieldSumData:
    properties:
      date:
        description: Date in YYYY-MM-DD format
        type: string
      sum:
        description: Sum value for the day
        type: number
    type: object
  services.DailyFieldsData:
    properties:
      date:
        description: Date in YYYY-MM-DD format
        type: string
      fields_data:
        additionalProperties:
          format: float64
          type: number
        description: All fields sum data for this date
        type: object
    type: object
  services.DuneQueryResultResponse:
    properties:
      comparison_data:
        allOf:
        - $ref: '#/definitions/db.JSONB'
        description: Historical period data for comparison
      contract_interaction:
        type: integer
      contract_interaction_change_rate:
        description: Contract interaction change rate compared to previous period
        type: number
      data:
        allOf:
        - $ref: '#/definitions/db.JSONB'
        description: Dynamic fields data
      data_change_rates:
        additionalProperties:
          format: float64
          type: number
        description: Dynamic fields change rates
        type: object
      query_date:
        type: string
      users:
        type: integer
      users_change_rate:
        description: Users change rate compared to previous period
        type: number
    type: object
  services.FieldSumData:
    properties:
      change_rate:
        type: number
      current_sum:
        type: number
      previous_sum:
        type: number
    type: object
  services.ProjectDataResponse:
    properties:
      chain_ids:
        items:
          type: string
        type: array
      contract_addresses:
        items:
          type: string
        type: array
      dune_query_id:
        type: string
      id:
        type: integer
      project_logo:
        type: string
      project_name:
        type: string
      query_result:
        allOf:
        - $ref: '#/definitions/services.DuneQueryResultResponse'
        description: New single result field
      query_results:
        description: For backward compatibility
        items:
          $ref: '#/definitions/services.DuneQueryResultResponse'
        type: array
      slug:
        type: string
      tags:
        items:
          type: string
        type: array
      twitter_user_name:
        type: string
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: API Support
  description: API for the Real-Time Contract Address service
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: Real-Time CA Service API
  version: "1.0"
paths:
  /admin/announcement-statistics/hide-list:
    get:
      consumes:
      - application/json
      description: Get the full list of hidden usernames for announcement statistics
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get announcement statistics hide list
      tags:
      - statistics
    post:
      consumes:
      - application/json
      description: Overwrite the hide user list for announcement statistics with the
        provided usernames array. The existing list is cleared and replaced.
      parameters:
      - description: Usernames to set as hidden
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/api.HideListRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Set announcement statistics hide list
      tags:
      - statistics
  /announcement-statistics:
    get:
      consumes:
      - application/json
      description: Get statistics for tweets with source_list_type="Projects" and
        ai_judgment="YES" grouped by Twitter user. Optional time filtering with start_time
        and end_time query parameters (Unix timestamps). Optional sorting with sort_field
        and sort_direction parameters.
      parameters:
      - description: Start time (Unix timestamp, inclusive)
        example: 1640995200
        in: query
        name: start_time
        type: integer
      - description: End time (Unix timestamp, inclusive)
        example: 1672531199
        in: query
        name: end_time
        type: integer
      - description: Field to sort by
        enum:
        - product_updates
        - business_data
        - ecosystem_partnership
        - profit_opportunity
        - industry_events
        - others
        - total
        - days_since_last_tweet
        in: query
        name: sort_field
        type: string
      - description: Sort direction
        enum:
        - asc
        - desc
        in: query
        name: sort_direction
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/api.UserAnnouncementStatsResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get announcement statistics
      tags:
      - statistics
  /announcement-statistics/daily:
    get:
      consumes:
      - application/json
      description: Get daily announcement statistics history starting from the day
        before current day (UTC+8 timezone)
      parameters:
      - description: 'Number of days to look back (default: 7)'
        in: query
        name: days
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/api.AnnouncementStatisticsDailyResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get daily announcement statistics
      tags:
      - statistics
  /announcement-statistics/paginated:
    get:
      consumes:
      - application/json
      description: Get statistics for tweets with source_list_type="Projects" and
        ai_judgment="YES" grouped by Twitter user. Supports pagination, time filtering,
        and sorting.
      parameters:
      - description: Start time (Unix timestamp, inclusive)
        example: 1640995200
        in: query
        name: start_time
        type: integer
      - description: End time (Unix timestamp, inclusive)
        example: 1672531199
        in: query
        name: end_time
        type: integer
      - description: Field to sort by
        enum:
        - product_updates
        - business_data
        - ecosystem_partnership
        - profit_opportunity
        - industry_events
        - others
        - total
        - days_since_last_tweet
        in: query
        name: sort_field
        type: string
      - description: Sort direction
        enum:
        - asc
        - desc
        in: query
        name: sort_direction
        type: string
      - description: 'Page number (default: 1)'
        example: 1
        in: query
        name: page
        type: integer
      - description: 'Page size (default: 20, max: 100)'
        example: 20
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.PaginatedAnnouncementStatsResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get announcement statistics with pagination
      tags:
      - statistics
  /announcement-statistics/total:
    get:
      consumes:
      - application/json
      description: Get total announcement count and 24-hour change percentage for
        all projects
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.AnnouncementStatisticsTotalResponse'
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get total announcement statistics
      tags:
      - statistics
  /cached-collections:
    get:
      consumes:
      - application/json
      description: Retrieves collections data from Redis cache with tags information
      parameters:
      - description: 'Page number (default: 1)'
        in: query
        name: page
        type: integer
      - description: 'Page size (default: 100)'
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GetTradeTokenInfoResponse'
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get cached collections data
      tags:
      - collections
  /collection-tags:
    get:
      consumes:
      - application/json
      description: Returns a list of all unique collection tags from cached collections
      produces:
      - application/json
      responses:
        "200":
          description: Collection tags list
          schema:
            items:
              type: string
            type: array
        "500":
          description: Internal server error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get all collection tags
      tags:
      - collections
  /dune/all-projects/daily-field-sum:
    get:
      consumes:
      - application/json
      description: Retrieve daily aggregated sum data for all binding projects within
        specified days range. Returns daily sum values for each field without comparison.
      parameters:
      - description: Field name to aggregate (e.g., contract_interaction, users, or
          any dynamic field). If not provided, returns aggregated data for all available
          fields
        in: query
        name: field
        type: string
      - description: Number of days for the analysis period (e.g., 7 for last 7 days,
          30 for last 30 days, 0 for all available data)
        in: query
        name: days
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: All projects daily field sum data
          schema:
            $ref: '#/definitions/services.AllProjectsDailyFieldSumResponse'
        "400":
          description: Invalid parameters
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Failed to get projects data
          schema:
            additionalProperties: true
            type: object
      summary: Get all projects daily field sum
      tags:
      - dune
  /dune/all-projects/field-sum-comparison:
    get:
      consumes:
      - application/json
      description: Retrieve aggregated sum data for all binding projects within specified
        days range and compare with the previous period of the same length. Returns
        sum values across all projects for the specified field.
      parameters:
      - description: Field name to aggregate (e.g., contract_interaction, users, or
          any dynamic field). If not provided, returns aggregated data for all available
          fields
        in: query
        name: field
        type: string
      - description: Number of days for the analysis period (e.g., 7 for last 7 days)
        in: query
        name: days
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: All projects field sum comparison data
          schema:
            $ref: '#/definitions/services.AllProjectsFieldSumComparisonResponse'
        "400":
          description: Invalid parameters
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Failed to get projects data
          schema:
            additionalProperties: true
            type: object
      summary: Get all projects field sum comparison
      tags:
      - dune
  /dune/bindings:
    get:
      consumes:
      - application/json
      description: Retrieve all Dune Twitter bindings with pagination support
      parameters:
      - description: 'Number of bindings to return (default: 100)'
        in: query
        name: limit
        type: integer
      - description: 'Number of bindings to skip (default: 0)'
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: List of Dune Twitter bindings
          schema:
            items:
              $ref: '#/definitions/db.DuneBindingResponse'
            type: array
        "500":
          description: Failed to get bindings
          schema:
            additionalProperties: true
            type: object
      summary: Get all Dune Twitter bindings
      tags:
      - dune
    post:
      consumes:
      - application/json
      description: Create new bindings between Dune queries and a Twitter user with
        category associations
      parameters:
      - description: Dune binding request with category-query mappings
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/api.DuneBindingRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Bindings created successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid request body
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Failed to create bindings
          schema:
            additionalProperties: true
            type: object
      summary: Create new Dune Twitter bindings
      tags:
      - dune
  /dune/bindings/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a Dune Twitter binding by its ID
      parameters:
      - description: Binding ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Binding deleted successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid binding ID
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Failed to delete binding
          schema:
            additionalProperties: true
            type: object
      summary: Delete a Dune Twitter binding
      tags:
      - dune
    get:
      consumes:
      - application/json
      description: Retrieve a specific Dune Twitter binding by its ID with category-query
        mappings
      parameters:
      - description: Binding ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Dune Twitter binding with category queries
          schema:
            $ref: '#/definitions/db.DuneBindingResponse'
        "400":
          description: Invalid binding ID
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Binding not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Failed to get binding
          schema:
            additionalProperties: true
            type: object
      summary: Get a specific Dune Twitter binding
      tags:
      - dune
    put:
      consumes:
      - application/json
      description: Update an existing Dune Twitter binding by its ID
      parameters:
      - description: Binding ID
        in: path
        name: id
        required: true
        type: integer
      - description: Updated binding data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/api.DuneBindingRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Binding updated successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid binding ID or request body
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Failed to update binding
          schema:
            additionalProperties: true
            type: object
      summary: Update a Dune Twitter binding
      tags:
      - dune
  /dune/bindings/{id}/categories:
    get:
      consumes:
      - application/json
      description: Get a specific binding with its associated categories and field
        definitions
      parameters:
      - description: Binding ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Binding with categories
          schema:
            $ref: '#/definitions/db.DuneTwitterBinding'
        "400":
          description: Invalid binding ID
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Binding not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Failed to get binding
          schema:
            additionalProperties: true
            type: object
      summary: Get binding with categories
      tags:
      - dune-bindings
    put:
      consumes:
      - application/json
      description: Associate a dune binding with multiple categories
      parameters:
      - description: Binding ID
        in: path
        name: id
        required: true
        type: integer
      - description: Category association request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/api.DuneBindingCategoryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Association successful
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Binding not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Failed to associate categories
          schema:
            additionalProperties: true
            type: object
      summary: Associate binding with categories
      tags:
      - dune-bindings
  /dune/bindings/{id}/period-comparison:
    get:
      consumes:
      - application/json
      description: Retrieve aggregated data for a specific binding within specified
        days range and compare with the previous period of the same length. Returns
        the same data structure as GetAllProjectsDataWithPeriodComparison but for
        a single binding. If field parameter is provided, only that field will be
        included in data and data_change_rates.
      parameters:
      - description: Dune binding ID
        in: path
        name: id
        required: true
        type: integer
      - description: Number of days for the analysis period (e.g., 7 for last 7 days)
        in: query
        name: days
        required: true
        type: integer
      - description: Optional field name to filter data (if provided, only this field
          will be included in data and data_change_rates)
        in: query
        name: field
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Binding project aggregated data with period comparison
          schema:
            $ref: '#/definitions/services.ProjectDataResponse'
        "400":
          description: Invalid parameters
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Binding not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Failed to get project data
          schema:
            additionalProperties: true
            type: object
      summary: Get binding project period comparison
      tags:
      - dune
  /dune/categories:
    get:
      consumes:
      - application/json
      description: Retrieve all dune categories with their field definitions
      produces:
      - application/json
      responses:
        "200":
          description: List of categories
          schema:
            items:
              $ref: '#/definitions/db.DuneCategory'
            type: array
        "500":
          description: Failed to get categories
          schema:
            additionalProperties: true
            type: object
      summary: Get all dune categories
      tags:
      - dune-categories
    post:
      consumes:
      - application/json
      description: Create a new category with field definitions for dune data parsing
      parameters:
      - description: Category creation request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/api.DuneCategoryRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Category created successfully
          schema:
            $ref: '#/definitions/db.DuneCategory'
        "400":
          description: Invalid request body
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Failed to create category
          schema:
            additionalProperties: true
            type: object
      summary: Create a new dune category
      tags:
      - dune-categories
  /dune/categories/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a dune category and all its associations
      parameters:
      - description: Category ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Category deleted successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid category ID
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Category not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Failed to delete category
          schema:
            additionalProperties: true
            type: object
      summary: Delete a dune category
      tags:
      - dune-categories
    get:
      consumes:
      - application/json
      description: Retrieve a specific dune category by its ID with field definitions
      parameters:
      - description: Category ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Dune category
          schema:
            $ref: '#/definitions/db.DuneCategory'
        "400":
          description: Invalid category ID
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Category not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Failed to get category
          schema:
            additionalProperties: true
            type: object
      summary: Get a specific dune category
      tags:
      - dune-categories
    put:
      consumes:
      - application/json
      description: Update an existing dune category and its field definitions
      parameters:
      - description: Category ID
        in: path
        name: id
        required: true
        type: integer
      - description: Updated category data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/api.DuneCategoryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Category updated successfully
          schema:
            $ref: '#/definitions/db.DuneCategory'
        "400":
          description: Invalid request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Category not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Failed to update category
          schema:
            additionalProperties: true
            type: object
      summary: Update a dune category
      tags:
      - dune-categories
  /dune/categories/{id}/bindings:
    get:
      consumes:
      - application/json
      description: Get all bindings associated with a specific category
      parameters:
      - description: Category ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: List of bindings
          schema:
            items:
              $ref: '#/definitions/db.DuneTwitterBinding'
            type: array
        "400":
          description: Invalid category ID
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Failed to get bindings
          schema:
            additionalProperties: true
            type: object
      summary: Get bindings by category
      tags:
      - dune-categories
  /dune/categories/{id}/daily-field-sum:
    get:
      consumes:
      - application/json
      description: Retrieve daily aggregated sum data for binding projects in a specific
        category within specified days range. Returns daily sum values for each field
        without comparison.
      parameters:
      - description: Category ID
        in: path
        name: id
        required: true
        type: integer
      - description: Field name to aggregate (e.g., contract_interaction, users, or
          any dynamic field). If not provided, returns aggregated data for all available
          fields
        in: query
        name: field
        type: string
      - description: Number of days for the analysis period (e.g., 7 for last 7 days,
          30 for last 30 days, 0 for all available data)
        in: query
        name: days
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Category projects daily field sum data
          schema:
            $ref: '#/definitions/services.CategoryDailyFieldSumResponse'
        "400":
          description: Invalid parameters
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Category not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Failed to get projects data
          schema:
            additionalProperties: true
            type: object
      summary: Get category projects daily field sum
      tags:
      - dune-categories
  /dune/categories/{id}/field-sum-comparison:
    get:
      consumes:
      - application/json
      description: Retrieve aggregated sum data for binding projects in a specific
        category within specified days range and compare with the previous period
        of the same length. Returns sum values for the specified field or all fields
        if field is not provided.
      parameters:
      - description: Category ID
        in: path
        name: id
        required: true
        type: integer
      - description: Field name to aggregate (e.g., contract_interaction, users, or
          any dynamic field). If not provided, returns aggregated data for all available
          fields
        in: query
        name: field
        type: string
      - description: Number of days for the analysis period (e.g., 7 for last 7 days)
        in: query
        name: days
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Category projects field sum comparison data
          schema:
            $ref: '#/definitions/services.CategoryFieldSumComparisonResponse'
        "400":
          description: Invalid parameters
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Category not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Failed to get projects data
          schema:
            additionalProperties: true
            type: object
      summary: Get category projects field sum comparison
      tags:
      - dune-categories
  /dune/categories/{id}/period-comparison:
    get:
      consumes:
      - application/json
      description: Retrieve aggregated data for binding projects in a specific category
        within specified days range and compare with the previous period of the same
        length. Returns the same data structure as GetAllProjectsDataWithPeriodComparison
        but filtered to category bindings. If field parameter is provided, only that
        field will be included in data and data_change_rates.
      parameters:
      - description: Category ID
        in: path
        name: id
        required: true
        type: integer
      - description: Number of days for the analysis period (e.g., 7 for last 7 days)
        in: query
        name: days
        required: true
        type: integer
      - description: Optional field name to filter data (if provided, only this field
          will be included in data and data_change_rates)
        in: query
        name: field
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Category projects aggregated data with period comparison
          schema:
            items:
              $ref: '#/definitions/services.ProjectDataResponse'
            type: array
        "400":
          description: Invalid parameters
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Category not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Failed to get projects data
          schema:
            additionalProperties: true
            type: object
      summary: Get category projects period comparison
      tags:
      - dune-categories
  /dune/categories/{id}/project-data/{user_name}:
    get:
      consumes:
      - application/json
      description: Retrieve Dune project data for a specific Twitter user and category
        ID with optional time range filtering
      parameters:
      - description: Twitter username
        in: path
        name: user_name
        required: true
        type: string
      - description: Category ID
        in: path
        name: id
        required: true
        type: string
      - description: Start time as Unix timestamp
        format: int64
        in: query
        name: start_time
        type: integer
      - description: End time as Unix timestamp
        format: int64
        in: query
        name: end_time
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Project data
          schema:
            $ref: '#/definitions/services.ProjectDataResponse'
        "400":
          description: Bad request - invalid parameters
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Get project data by Twitter user and category
      tags:
      - dune
  /dune/fetch-all:
    post:
      consumes:
      - application/json
      description: Manually triggers the FetchAllDuneData method to fetch data for
        all Dune query bindings
      parameters:
      - description: Optional API key for access control
        in: header
        name: X-API-Key
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Fetch triggered successfully
          schema:
            $ref: '#/definitions/api.DuneFetchAllResponse'
        "500":
          description: Failed to trigger fetch
          schema:
            additionalProperties: true
            type: object
      summary: Manually trigger Dune data fetch
      tags:
      - dune
  /dune/project-data/:id:
    get:
      consumes:
      - application/json
      description: Retrieve project data from Dune Analytics by Twitter username and
        time range
      parameters:
      - description: Query ID
        in: path
        name: id
        required: true
        type: string
      - description: Start time (Unix timestamp)
        in: query
        name: start_time
        type: string
      - description: End time (Unix timestamp)
        in: query
        name: end_time
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Project data from Dune Analytics
          schema:
            $ref: '#/definitions/services.ProjectDataResponse'
        "400":
          description: Invalid parameters
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Failed to get project data
          schema:
            additionalProperties: true
            type: object
      summary: Get Dune project data
      tags:
      - dune
  /dune/projects/period-comparison:
    get:
      consumes:
      - application/json
      description: Retrieve aggregated data for all Twitter-Dune binding projects
        within specified days range and compare with previous period
      parameters:
      - description: Number of days for the analysis period (e.g., 7 for last 7 days)
        in: query
        name: days
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Projects aggregated data with period comparison
          schema:
            items:
              $ref: '#/definitions/services.ProjectDataResponse'
            type: array
        "400":
          description: Invalid parameters
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Failed to get projects data
          schema:
            additionalProperties: true
            type: object
      summary: Get aggregated data for all Dune projects with period comparison
      tags:
      - dune
  /excluded-twitter-users:
    get:
      consumes:
      - application/json
      description: Retrieves all excluded Twitter users
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/db.ExcludedTwitterUser'
            type: array
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get excluded Twitter users
      tags:
      - excluded-users
    post:
      consumes:
      - application/json
      description: Adds a Twitter user to the exclusion list for statistics and API
        returns
      parameters:
      - description: Excluded user request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/api.ExcludedTwitterUserRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Add excluded Twitter user
      tags:
      - excluded-users
  /excluded-twitter-users/{twitter_user_name}:
    delete:
      consumes:
      - application/json
      description: Removes a Twitter user from the exclusion list
      parameters:
      - description: Twitter user name
        in: path
        name: twitter_user_name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Remove excluded Twitter user
      tags:
      - excluded-users
  /health:
    get:
      consumes:
      - application/json
      description: Returns 200 OK if the service is healthy
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Health check endpoint
      tags:
      - system
  /list-tweets:
    get:
      consumes:
      - application/json
      description: Retrieves a list of tweets related to AI Agent with filtering options
      parameters:
      - description: 'Number of tweets to return (default: 20)'
        in: query
        name: limit
        type: integer
      - description: 'Offset for pagination (default: 0)'
        in: query
        name: offset
        type: integer
      - description: Filter by content type (tweet, article, ALL)
        in: query
        name: content_type
        type: string
      - description: Filter by source type (KOLs, Projects, ALL)
        in: query
        name: source_type
        type: string
      - description: Filter by notice_type
        in: query
        name: notice_type
        type: string
      - description: Filter by user_id
        in: query
        name: user_id
        type: string
      - description: Filter by user_name
        in: query
        name: user_name
        type: string
      - collectionFormat: csv
        description: Filter by tags (comma-separated, e.g. 'tag1,tag2')
        in: query
        items:
          type: string
        name: tags
        type: array
      - collectionFormat: csv
        description: Filter by collection tags (comma-separated, e.g. 'tag1,tag2')
        in: query
        items:
          type: string
        name: collection_tags
        type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/api.TweetResponse'
            type: array
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get AI Agent related tweets
      tags:
      - tweets
  /recognized-ca/{address}/{chain_id}:
    get:
      consumes:
      - application/json
      description: Retrieves a specific recognized contract address by its address
        and chain ID
      parameters:
      - description: Contract address
        in: path
        name: address
        required: true
        type: string
      - description: Chain ID
        in: path
        name: chain_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.CAResponse'
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get a single recognized contract address
      tags:
      - contract-addresses
  /recognized-cas:
    get:
      consumes:
      - application/json
      description: Retrieves a list of recognized contract addresses
      parameters:
      - description: 'Number of CAs to return (default: 20)'
        in: query
        name: limit
        type: integer
      - description: 'Offset for pagination (default: 0)'
        in: query
        name: offset
        type: integer
      - collectionFormat: csv
        description: Filter by tags (comma-separated, e.g. 'tag1,tag2')
        in: query
        items:
          type: string
        name: tags
        type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/api.CAResponse'
            type: array
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get recognized contract addresses
      tags:
      - contract-addresses
  /telegram-notification-stats:
    get:
      consumes:
      - application/json
      description: Returns statistics about Telegram notifications including total
        sent, duplicates prevented, etc.
      produces:
      - application/json
      responses:
        "200":
          description: Notification statistics
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get Telegram notification statistics
      tags:
      - notifications
  /trigger-collections-cache:
    post:
      consumes:
      - application/json
      description: Triggers the collections cache update task manually for testing
        purposes
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Manually trigger collections cache update
      tags:
      - system
  /tweet/{tweet_id}:
    get:
      consumes:
      - application/json
      description: Retrieves a single tweet by its ID
      parameters:
      - description: Tweet ID
        in: path
        name: tweet_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.TweetResponse'
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get a tweet by ID
      tags:
      - tweets
  /tweets:
    get:
      consumes:
      - application/json
      description: Retrieves a list of tweets
      parameters:
      - description: 'Number of tweets to return (default: 20)'
        in: query
        name: limit
        type: integer
      - description: 'Offset for pagination (default: 0)'
        in: query
        name: offset
        type: integer
      - collectionFormat: csv
        description: Filter by tags (comma-separated, e.g. 'tag1,tag2')
        in: query
        items:
          type: string
        name: tags
        type: array
      - collectionFormat: csv
        description: Filter by collection tags (comma-separated, e.g. 'tag1,tag2')
        in: query
        items:
          type: string
        name: collection_tags
        type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/api.TweetResponse'
            type: array
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get tweets
      tags:
      - tweets
  /webhook/twitter:
    post:
      consumes:
      - application/json
      description: Processes webhook events from Twitter
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Handle Twitter webhook
      tags:
      - webhooks
securityDefinitions:
  ApiKeyAuth:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
