#!/bin/bash

# Test script for the new paginated category period comparison API
# This script tests the new /dune/categories/:id/period-comparison/paginated endpoint

BASE_URL="http://localhost:8080/api"
CATEGORY_ID=1  # Change this to a valid category ID in your database

echo "=== Testing Category Period Comparison Paginated API ==="
echo "Base URL: $BASE_URL"
echo "Category ID: $CATEGORY_ID"
echo ""

# Test 1: Basic paginated request
echo "Test 1: Basic paginated request (page=1, page_size=5)"
curl -s -X GET "$BASE_URL/dune/categories/$CATEGORY_ID/period-comparison/paginated?days=7&page=1&page_size=5" \
  -H "Content-Type: application/json" | jq '.'
echo ""

# Test 2: Test with field filter
echo "Test 2: With field filter (contract_interaction)"
curl -s -X GET "$BASE_URL/dune/categories/$CATEGORY_ID/period-comparison/paginated?days=7&field=contract_interaction&page=1&page_size=3" \
  -H "Content-Type: application/json" | jq '.'
echo ""

# Test 3: Test with sorting
echo "Test 3: With sorting (sort by project_name desc)"
curl -s -X GET "$BASE_URL/dune/categories/$CATEGORY_ID/period-comparison/paginated?days=7&page=1&page_size=3&sort_field=project_name&sort_direction=desc" \
  -H "Content-Type: application/json" | jq '.'
echo ""

# Test 4: Test second page
echo "Test 4: Second page"
curl -s -X GET "$BASE_URL/dune/categories/$CATEGORY_ID/period-comparison/paginated?days=7&page=2&page_size=3" \
  -H "Content-Type: application/json" | jq '.'
echo ""

# Test 5: Compare with non-paginated version (first few results should match)
echo "Test 5: Compare with non-paginated version"
echo "Non-paginated (first 3 results):"
curl -s -X GET "$BASE_URL/dune/categories/$CATEGORY_ID/period-comparison?days=7" \
  -H "Content-Type: application/json" | jq '.[0:3]'
echo ""
echo "Paginated (page=1, page_size=3):"
curl -s -X GET "$BASE_URL/dune/categories/$CATEGORY_ID/period-comparison/paginated?days=7&page=1&page_size=3" \
  -H "Content-Type: application/json" | jq '.data'
echo ""

# Test 6: Error cases
echo "Test 6: Error cases"
echo "Missing days parameter:"
curl -s -X GET "$BASE_URL/dune/categories/$CATEGORY_ID/period-comparison/paginated?page=1&page_size=5" \
  -H "Content-Type: application/json" | jq '.'
echo ""

echo "Invalid sort field:"
curl -s -X GET "$BASE_URL/dune/categories/$CATEGORY_ID/period-comparison/paginated?days=7&page=1&page_size=5&sort_field=invalid_field" \
  -H "Content-Type: application/json" | jq '.'
echo ""

echo "Invalid category ID:"
curl -s -X GET "$BASE_URL/dune/categories/99999/period-comparison/paginated?days=7&page=1&page_size=5" \
  -H "Content-Type: application/json" | jq '.'
echo ""

echo "=== Test completed ==="
